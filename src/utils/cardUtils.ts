import { OracleCard, DrawnCard, SpreadType, CardAnimation } from '../types/Card';

export const shuffleArray = <T>(array: T[]): T[] => {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

export const drawCards = (
  deck: OracleCard[],
  count: number
): DrawnCard[] => {
  const shuffledDeck = shuffleArray(deck);
  return shuffledDeck.slice(0, count).map((card, index) => ({
    ...card,
    id: `card-${Date.now()}-${index}`,
    isRevealed: false,
    isReversed: Math.random() < 0.3, // 30% chance of being reversed
    position: index,
    isSelected: false,
    isHovered: false
  }));
};

export const getCardCount = (spreadType: SpreadType): number => {
  return spreadType === 'single' ? 1 : 3;
};

export const getSpreadPositions = (spreadType: SpreadType) => {
  if (spreadType === 'single') {
    return [{ x: 0, y: 0, label: 'Your Card' }];
  }

  return [
    { x: -200, y: 0, label: 'Past' },
    { x: 0, y: 0, label: 'Present' },
    { x: 200, y: 0, label: 'Future' }
  ];
};

// Generate deck spread positions in an arc
export const generateDeckSpreadPositions = (
  cardCount: number,
  containerWidth: number = 800,
  containerHeight: number = 400
): CardAnimation[] => {
  const positions: CardAnimation[] = [];
  const arcRadius = Math.min(containerWidth * 0.3, 200);
  const arcAngle = Math.PI * 0.6; // 108 degrees
  const startAngle = -arcAngle / 2;

  // Handle single card case
  if (cardCount === 1) {
    positions.push({
      x: 0,
      y: 0,
      rotation: 0,
      scale: 1,
      zIndex: 0,
      opacity: 1
    });
    return positions;
  }

  for (let i = 0; i < cardCount; i++) {
    const angle = startAngle + (arcAngle * i) / (cardCount - 1);
    const x = Math.cos(angle) * arcRadius;
    const y = Math.sin(angle) * arcRadius * 0.5; // Flatten the arc
    const rotation = (angle * 180) / Math.PI * 0.3; // Subtle rotation

    positions.push({
      x,
      y,
      rotation,
      scale: 1,
      zIndex: i,
      opacity: 1
    });
  }

  return positions;
};

// Generate stacked deck positions
export const generateStackedDeckPositions = (cardCount: number): CardAnimation[] => {
  const positions: CardAnimation[] = [];
  const maxVisible = Math.min(cardCount, 8);

  for (let i = 0; i < maxVisible; i++) {
    positions.push({
      x: i * 2,
      y: i * -1,
      rotation: Math.random() * 4 - 2, // Slight random rotation
      scale: 1,
      zIndex: maxVisible - i,
      opacity: 1
    });
  }

  return positions;
};

// Calculate card selection animation
export const getCardSelectionAnimation = (
  fromPosition: CardAnimation,
  toPosition: { x: number; y: number },
  isSelected: boolean
): CardAnimation => {
  if (isSelected) {
    return {
      ...fromPosition,
      x: toPosition.x,
      y: toPosition.y - 20, // Lift the card
      scale: 1.1,
      zIndex: 1000,
      opacity: 1,
      rotation: 0
    };
  }

  return fromPosition;
};

// Generate reading positions for cards
export const getReadingPositions = (
  spreadType: SpreadType,
  containerWidth: number = 1200
): { x: number; y: number; label: string }[] => {
  if (spreadType === 'single') {
    return [{ x: 0, y: 0, label: 'Your Card' }];
  }

  const cardSpacing = Math.min(containerWidth * 0.25, 300);
  return [
    { x: -cardSpacing, y: 0, label: 'Past' },
    { x: 0, y: 0, label: 'Present' },
    { x: cardSpacing, y: 0, label: 'Future' }
  ];
};

export const sleep = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};
