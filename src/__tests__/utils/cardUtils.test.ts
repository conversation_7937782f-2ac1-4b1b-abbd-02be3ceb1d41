import {
  drawCards,
  getCardCount,
  getSpreadPositions,
  getReadingPositions,
  generateDeckSpreadPositions,
  generateStackedDeckPositions,
  getCardSelectionAnimation,
  sleep,
  shuffleArray
} from '../../utils/cardUtils';
import { SpreadType, OracleCard, CardAnimation } from '../../types/Card';

// Mock oracle cards for testing
const mockCards: OracleCard[] = [
  {
    title: 'Test Card 1',
    suite: 'Test Suite',
    upright_meaning: 'Test upright meaning 1',
    upright_key_interpretation: 'Test upright interpretation 1',
    upright_affirmation: 'Test upright affirmation 1',
    upright_journal_prompt: 'Test upright prompt 1',
    reversal_key_interpretation: 'Test reversal interpretation 1',
    reversal_journal_prompt: 'Test reversal prompt 1',
    reversal_meaning: 'Test reversal meaning 1',
    reversal_affirmation: 'Test reversal affirmation 1'
  },
  {
    title: 'Test Card 2',
    suite: 'Test Suite',
    upright_meaning: 'Test upright meaning 2',
    upright_key_interpretation: 'Test upright interpretation 2',
    upright_affirmation: 'Test upright affirmation 2',
    upright_journal_prompt: 'Test upright prompt 2',
    reversal_key_interpretation: 'Test reversal interpretation 2',
    reversal_journal_prompt: 'Test reversal prompt 2',
    reversal_meaning: 'Test reversal meaning 2',
    reversal_affirmation: 'Test reversal affirmation 2'
  },
  {
    title: 'Test Card 3',
    suite: 'Test Suite',
    upright_meaning: 'Test upright meaning 3',
    upright_key_interpretation: 'Test upright interpretation 3',
    upright_affirmation: 'Test upright affirmation 3',
    upright_journal_prompt: 'Test upright prompt 3',
    reversal_key_interpretation: 'Test reversal interpretation 3',
    reversal_journal_prompt: 'Test reversal prompt 3',
    reversal_meaning: 'Test reversal meaning 3',
    reversal_affirmation: 'Test reversal affirmation 3'
  }
];

describe('cardUtils', () => {
  describe('shuffleArray', () => {
    test('returns array with same length', () => {
      const result = shuffleArray(mockCards);
      expect(result).toHaveLength(mockCards.length);
    });

    test('returns array with same elements', () => {
      const result = shuffleArray(mockCards);
      expect(result).toEqual(expect.arrayContaining(mockCards));
    });

    test('does not mutate original array', () => {
      const original = [...mockCards];
      shuffleArray(mockCards);
      expect(mockCards).toEqual(original);
    });
  });

  describe('getCardCount', () => {
    test('returns 1 for single spread', () => {
      expect(getCardCount('single')).toBe(1);
    });

    test('returns 3 for three spread', () => {
      expect(getCardCount('three')).toBe(3);
    });
  });

  describe('getSpreadPositions', () => {
    test('returns correct positions for single spread', () => {
      const positions = getSpreadPositions('single');
      expect(positions).toHaveLength(1);
      expect(positions[0]).toEqual({
        x: 0,
        y: 0,
        label: 'Your Card'
      });
    });

    test('returns correct positions for three spread', () => {
      const positions = getSpreadPositions('three');
      expect(positions).toHaveLength(3);
      expect(positions[0].label).toBe('Past');
      expect(positions[1].label).toBe('Present');
      expect(positions[2].label).toBe('Future');
    });
  });

  describe('drawCards', () => {
    test('draws correct number of cards', () => {
      const cards = drawCards(mockCards, 2);
      expect(cards).toHaveLength(2);
      cards.forEach(card => {
        expect(card).toHaveProperty('id');
        expect(card).toHaveProperty('title');
        expect(card).toHaveProperty('isRevealed', false);
        expect(card).toHaveProperty('position');
      });
    });

    test('draws single card', () => {
      const cards = drawCards(mockCards, 1);
      expect(cards).toHaveLength(1);
      expect(cards[0]).toHaveProperty('id');
      expect(cards[0]).toHaveProperty('title');
      expect(cards[0]).toHaveProperty('isRevealed', false);
    });

    test('draws all available cards when count exceeds deck size', () => {
      const cards = drawCards(mockCards, 10);
      expect(cards).toHaveLength(mockCards.length);
    });

    test('assigns unique IDs to cards', () => {
      const cards = drawCards(mockCards, 3);
      const ids = cards.map(card => card.id);
      const uniqueIds = [...new Set(ids)];
      expect(uniqueIds).toHaveLength(ids.length);
    });

    test('assigns correct positions', () => {
      const cards = drawCards(mockCards, 3);
      cards.forEach((card, index) => {
        expect(card.position).toBe(index);
      });
    });
  });

  describe('getReadingPositions', () => {
    test('returns correct positions for single spread', () => {
      const positions = getReadingPositions('single', 1200);
      expect(positions).toHaveLength(1);
      expect(positions[0]).toEqual({
        x: 0,
        y: 0,
        label: 'Your Card'
      });
    });

    test('returns correct positions for three spread', () => {
      const positions = getReadingPositions('three', 1200);
      expect(positions).toHaveLength(3);
      expect(positions[0].label).toBe('Past');
      expect(positions[1].label).toBe('Present');
      expect(positions[2].label).toBe('Future');
      expect(positions[0].x).toBeLessThan(0);
      expect(positions[1].x).toBe(0);
      expect(positions[2].x).toBeGreaterThan(0);
    });

    test('adjusts spacing based on container width', () => {
      const narrowPositions = getReadingPositions('three', 600);
      const widePositions = getReadingPositions('three', 1800);

      expect(Math.abs(narrowPositions[0].x)).toBeLessThan(Math.abs(widePositions[0].x));
    });
  });

  describe('generateDeckSpreadPositions', () => {
    test('generates correct number of positions', () => {
      const positions = generateDeckSpreadPositions(10);
      expect(positions).toHaveLength(10);
    });

    test('each position has required properties', () => {
      const positions = generateDeckSpreadPositions(5);
      positions.forEach(position => {
        expect(position).toHaveProperty('x');
        expect(position).toHaveProperty('y');
        expect(position).toHaveProperty('rotation');
        expect(position).toHaveProperty('scale');
        expect(position).toHaveProperty('zIndex');
        expect(position).toHaveProperty('opacity');
      });
    });

    test('positions form an arc pattern', () => {
      const positions = generateDeckSpreadPositions(5);
      const xValues = positions.map(p => p.x);

      // Should have negative and positive x values for arc (with multiple cards)
      expect(xValues.some(x => x < 0)).toBe(true);
      expect(xValues.some(x => x > 0)).toBe(true);
    });

    test('handles single card', () => {
      const positions = generateDeckSpreadPositions(1);
      expect(positions).toHaveLength(1);
      expect(positions[0].x).toBe(0);
    });
  });

  describe('generateStackedDeckPositions', () => {
    test('generates positions for stacked deck', () => {
      const positions = generateStackedDeckPositions(20);
      expect(positions.length).toBeLessThanOrEqual(8); // Max visible cards
    });

    test('each position has required properties', () => {
      const positions = generateStackedDeckPositions(5);
      positions.forEach(position => {
        expect(position).toHaveProperty('x');
        expect(position).toHaveProperty('y');
        expect(position).toHaveProperty('rotation');
        expect(position).toHaveProperty('scale');
        expect(position).toHaveProperty('zIndex');
        expect(position).toHaveProperty('opacity');
      });
    });

    test('creates stacked effect with increasing offsets', () => {
      const positions = generateStackedDeckPositions(5);
      for (let i = 1; i < positions.length; i++) {
        expect(positions[i].x).toBeGreaterThan(positions[i-1].x);
        expect(positions[i].y).toBeLessThan(positions[i-1].y);
      }
    });
  });

  describe('getCardSelectionAnimation', () => {
    const mockFromPosition: CardAnimation = {
      x: 0,
      y: 0,
      rotation: 0,
      scale: 1,
      zIndex: 1,
      opacity: 1
    };

    const mockToPosition = { x: 100, y: 50 };

    test('returns lifted position when selected', () => {
      const result = getCardSelectionAnimation(mockFromPosition, mockToPosition, true);

      expect(result.x).toBe(mockToPosition.x);
      expect(result.y).toBe(mockToPosition.y - 20); // Lifted
      expect(result.scale).toBe(1.1);
      expect(result.zIndex).toBe(1000);
      expect(result.rotation).toBe(0);
    });

    test('returns original position when not selected', () => {
      const result = getCardSelectionAnimation(mockFromPosition, mockToPosition, false);
      expect(result).toEqual(mockFromPosition);
    });
  });

  describe('sleep', () => {
    test('resolves after specified time', async () => {
      const start = Date.now();
      await sleep(100);
      const end = Date.now();
      expect(end - start).toBeGreaterThanOrEqual(90); // Allow some tolerance
    });

    test('resolves immediately for 0 ms', async () => {
      const start = Date.now();
      await sleep(0);
      const end = Date.now();
      expect(end - start).toBeLessThan(50);
    });
  });
});
