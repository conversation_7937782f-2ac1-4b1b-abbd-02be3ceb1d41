import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { ImmersiveDeck } from '../../components/ImmersiveDeck';
import { fallingBirdTheme } from '../../theme/muiTheme';
import { OracleCard, DeckState } from '../../types/Card';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => children,
}));

// Mock card back image
jest.mock('../../assets/card-back.png', () => 'mock-card-back.png');

const mockCards: OracleCard[] = [
  {
    title: 'Test Card 1',
    suite: 'Test Suite',
    upright_meaning: 'Test meaning 1',
    upright_key_interpretation: 'Test interpretation 1',
    upright_affirmation: 'Test affirmation 1',
    upright_journal_prompt: 'Test prompt 1',
    reversal_key_interpretation: 'Test reversal interpretation 1',
    reversal_journal_prompt: 'Test reversal prompt 1',
    reversal_meaning: 'Test reversal meaning 1',
    reversal_affirmation: 'Test reversal affirmation 1'
  },
  {
    title: 'Test Card 2',
    suite: 'Test Suite',
    upright_meaning: 'Test meaning 2',
    upright_key_interpretation: 'Test interpretation 2',
    upright_affirmation: 'Test affirmation 2',
    upright_journal_prompt: 'Test prompt 2',
    reversal_key_interpretation: 'Test reversal interpretation 2',
    reversal_journal_prompt: 'Test reversal prompt 2',
    reversal_meaning: 'Test reversal meaning 2',
    reversal_affirmation: 'Test reversal affirmation 2'
  },
  {
    title: 'Test Card 3',
    suite: 'Test Suite',
    upright_meaning: 'Test meaning 3',
    upright_key_interpretation: 'Test interpretation 3',
    upright_affirmation: 'Test affirmation 3',
    upright_journal_prompt: 'Test prompt 3',
    reversal_key_interpretation: 'Test reversal interpretation 3',
    reversal_journal_prompt: 'Test reversal prompt 3',
    reversal_meaning: 'Test reversal meaning 3',
    reversal_affirmation: 'Test reversal affirmation 3'
  }
];

const defaultProps = {
  cards: mockCards,
  deckState: 'stacked' as DeckState,
  onCardSelect: jest.fn(),
  onSpreadComplete: jest.fn(),
  selectedCardCount: 0,
  maxSelections: 1,
  isShuffling: false
};

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={fallingBirdTheme}>
      {component}
    </ThemeProvider>
  );
};

describe('ImmersiveDeck', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    test('renders deck container', () => {
      renderWithTheme(<ImmersiveDeck {...defaultProps} />);

      const deckContainer = screen.getByTestId('immersive-deck');
      expect(deckContainer).toBeInTheDocument();
    });

    test('renders cards based on deck state', () => {
      renderWithTheme(<ImmersiveDeck {...defaultProps} deckState="stacked" />);
      
      // Should render up to 8 cards for stacked state
      const cardImages = screen.getAllByAltText('Oracle card back');
      expect(cardImages.length).toBeLessThanOrEqual(8);
    });

    test('renders selection counter when in spread state', () => {
      renderWithTheme(
        <ImmersiveDeck 
          {...defaultProps} 
          deckState="spread" 
          selectedCardCount={1}
          maxSelections={3}
        />
      );
      
      expect(screen.getByText('1 of 3 cards selected')).toBeInTheDocument();
    });

    test('does not render selection counter when stacked', () => {
      renderWithTheme(<ImmersiveDeck {...defaultProps} deckState="stacked" />);
      
      expect(screen.queryByText(/cards selected/)).not.toBeInTheDocument();
    });
  });

  describe('Card Interactions', () => {
    test('calls onCardSelect when card is clicked in spread state', () => {
      const mockOnCardSelect = jest.fn();
      renderWithTheme(
        <ImmersiveDeck 
          {...defaultProps} 
          deckState="spread"
          onCardSelect={mockOnCardSelect}
        />
      );
      
      const cards = screen.getAllByAltText('Oracle card back');
      fireEvent.click(cards[0]);
      
      expect(mockOnCardSelect).toHaveBeenCalledWith(0);
    });

    test('does not call onCardSelect when card is clicked in stacked state', () => {
      const mockOnCardSelect = jest.fn();
      renderWithTheme(
        <ImmersiveDeck 
          {...defaultProps} 
          deckState="stacked"
          onCardSelect={mockOnCardSelect}
        />
      );
      
      const cards = screen.getAllByAltText('Oracle card back');
      fireEvent.click(cards[0]);
      
      expect(mockOnCardSelect).not.toHaveBeenCalled();
    });

    test('does not allow selection when max selections reached', () => {
      const mockOnCardSelect = jest.fn();
      renderWithTheme(
        <ImmersiveDeck 
          {...defaultProps} 
          deckState="spread"
          onCardSelect={mockOnCardSelect}
          selectedCardCount={1}
          maxSelections={1}
        />
      );
      
      const cards = screen.getAllByAltText('Oracle card back');
      fireEvent.click(cards[1]);
      
      expect(mockOnCardSelect).not.toHaveBeenCalled();
    });

    test('shows selection indicator on selected cards', () => {
      renderWithTheme(
        <ImmersiveDeck 
          {...defaultProps} 
          deckState="selecting"
          selectedCardCount={1}
        />
      );
      
      // After selecting a card, it should show a checkmark
      const cards = screen.getAllByAltText('Oracle card back');
      fireEvent.click(cards[0]);
      
      // The component should update to show selection state
      expect(cards[0].closest('.deck-card')).toBeInTheDocument();
    });
  });

  describe('Deck States', () => {
    test('handles stacked state', () => {
      renderWithTheme(<ImmersiveDeck {...defaultProps} deckState="stacked" />);
      
      const cards = screen.getAllByAltText('Oracle card back');
      expect(cards.length).toBeLessThanOrEqual(8);
    });

    test('handles spreading state', async () => {
      const mockOnSpreadComplete = jest.fn();
      renderWithTheme(
        <ImmersiveDeck 
          {...defaultProps} 
          deckState="spreading"
          onSpreadComplete={mockOnSpreadComplete}
        />
      );
      
      // Should call onSpreadComplete after animation
      await waitFor(() => {
        expect(mockOnSpreadComplete).toHaveBeenCalled();
      }, { timeout: 2000 });
    });

    test('handles spread state', () => {
      renderWithTheme(<ImmersiveDeck {...defaultProps} deckState="spread" />);
      
      const cards = screen.getAllByAltText('Oracle card back');
      expect(cards.length).toBeLessThanOrEqual(15);
    });

    test('handles selecting state', () => {
      renderWithTheme(<ImmersiveDeck {...defaultProps} deckState="selecting" />);
      
      expect(screen.getByText('0 of 1 cards selected')).toBeInTheDocument();
    });
  });

  describe('Shuffling Animation', () => {
    test('applies shuffling animation when isShuffling is true', () => {
      renderWithTheme(<ImmersiveDeck {...defaultProps} isShuffling={true} />);
      
      const cards = screen.getAllByAltText('Oracle card back');
      expect(cards.length).toBeGreaterThan(0);
      
      // Cards should have the deck-card class for animation
      cards.forEach(card => {
        expect(card.closest('.deck-card')).toBeInTheDocument();
      });
    });

    test('does not apply shuffling animation when isShuffling is false', () => {
      renderWithTheme(<ImmersiveDeck {...defaultProps} isShuffling={false} />);
      
      const cards = screen.getAllByAltText('Oracle card back');
      expect(cards.length).toBeGreaterThan(0);
    });
  });

  describe('Accessibility', () => {
    test('cards have proper alt text', () => {
      renderWithTheme(<ImmersiveDeck {...defaultProps} />);
      
      const cards = screen.getAllByAltText('Oracle card back');
      expect(cards.length).toBeGreaterThan(0);
    });

    test('selection counter has proper text content', () => {
      renderWithTheme(
        <ImmersiveDeck 
          {...defaultProps} 
          deckState="spread"
          selectedCardCount={2}
          maxSelections={3}
        />
      );
      
      const counter = screen.getByText('2 of 3 cards selected');
      expect(counter).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    test('handles empty cards array', () => {
      renderWithTheme(<ImmersiveDeck {...defaultProps} cards={[]} />);
      
      const cards = screen.queryAllByAltText('Oracle card back');
      expect(cards).toHaveLength(0);
    });

    test('handles large number of cards', () => {
      const manyCards = Array(50).fill(null).map((_, index) => ({
        ...mockCards[0],
        title: `Card ${index + 1}`
      }));
      
      renderWithTheme(<ImmersiveDeck {...defaultProps} cards={manyCards} deckState="spread" />);
      
      const cards = screen.getAllByAltText('Oracle card back');
      expect(cards.length).toBeLessThanOrEqual(15); // Should limit to 15 in spread
    });

    test('handles maxSelections of 0', () => {
      const mockOnCardSelect = jest.fn();
      renderWithTheme(
        <ImmersiveDeck 
          {...defaultProps} 
          deckState="spread"
          onCardSelect={mockOnCardSelect}
          maxSelections={0}
        />
      );
      
      const cards = screen.getAllByAltText('Oracle card back');
      fireEvent.click(cards[0]);
      
      expect(mockOnCardSelect).not.toHaveBeenCalled();
    });
  });
});
