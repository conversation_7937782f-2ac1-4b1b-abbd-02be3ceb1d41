import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { EnhancedCard } from '../../components/EnhancedCard';
import { fallingBirdTheme } from '../../theme/muiTheme';
import { DrawnCard } from '../../types/Card';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => children,
}));

// Mock card back image
jest.mock('../../assets/card-back.png', () => 'mock-card-back.png');

// Mock Material-UI icons
jest.mock('@mui/icons-material', () => ({
  ExpandMore: () => <div data-testid="expand-more-icon" />,
  ExpandLess: () => <div data-testid="expand-less-icon" />,
  AutoStories: () => <div data-testid="auto-stories-icon" />,
}));

const mockCard: DrawnCard = {
  id: 'test-card-1',
  title: 'Test Card',
  suite: 'Test Suite',
  upright_meaning: 'This is the upright meaning of the test card.',
  upright_key_interpretation: 'Upright interpretation',
  upright_affirmation: 'I am testing this card.',
  upright_journal_prompt: 'What does this test card mean to you?',
  reversal_key_interpretation: 'Reversal interpretation',
  reversal_journal_prompt: 'How does the reversal apply to your situation?',
  reversal_meaning: 'This is the reversal meaning of the test card.',
  reversal_affirmation: 'I embrace the reversal energy.',
  isRevealed: false,
  isReversed: false,
  position: 0
};

const mockPosition = {
  x: 0,
  y: 0,
  label: 'Test Position'
};

const defaultProps = {
  card: mockCard,
  onReveal: jest.fn(),
  position: mockPosition,
  isDrawing: false,
  isReading: false,
  containerWidth: 1200
};

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={fallingBirdTheme}>
      {component}
    </ThemeProvider>
  );
};

describe('EnhancedCard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    test('renders card container', () => {
      renderWithTheme(<EnhancedCard {...defaultProps} />);
      
      expect(screen.getByText('Test Position')).toBeInTheDocument();
    });

    test('renders card back when not revealed', () => {
      renderWithTheme(<EnhancedCard {...defaultProps} />);
      
      expect(screen.getByAltText('Card back')).toBeInTheDocument();
      expect(screen.getByText('Click to reveal')).toBeInTheDocument();
    });

    test('renders card front when revealed', () => {
      const revealedCard = { ...mockCard, isRevealed: true };
      renderWithTheme(<EnhancedCard {...defaultProps} card={revealedCard} />);
      
      expect(screen.getByText('Test Card')).toBeInTheDocument();
      expect(screen.getByText('Test Suite')).toBeInTheDocument();
      expect(screen.getByText('This is the upright meaning of the test card.')).toBeInTheDocument();
      expect(screen.getByText('Upright interpretation')).toBeInTheDocument();
      expect(screen.getByText('"I am testing this card."')).toBeInTheDocument();
    });

    test('renders reversed card content when card is reversed', () => {
      const reversedCard = { ...mockCard, isRevealed: true, isReversed: true };
      renderWithTheme(<EnhancedCard {...defaultProps} card={reversedCard} />);
      
      expect(screen.getByText('Test Card')).toBeInTheDocument();
      expect(screen.getByText('Reversed')).toBeInTheDocument();
      expect(screen.getByText('This is the reversal meaning of the test card.')).toBeInTheDocument();
      expect(screen.getByText('Reversal interpretation')).toBeInTheDocument();
      expect(screen.getByText('"I embrace the reversal energy."')).toBeInTheDocument();
    });

    test('shows guidance toggle button when revealed', () => {
      const revealedCard = { ...mockCard, isRevealed: true };
      renderWithTheme(<EnhancedCard {...defaultProps} card={revealedCard} />);
      
      expect(screen.getByTestId('auto-stories-icon')).toBeInTheDocument();
      expect(screen.getByTestId('expand-more-icon')).toBeInTheDocument();
    });
  });

  describe('Card Interactions', () => {
    test('calls onReveal when unrevealed card is clicked', async () => {
      const mockOnReveal = jest.fn();
      renderWithTheme(<EnhancedCard {...defaultProps} onReveal={mockOnReveal} />);
      
      const cardElement = screen.getByAltText('Card back').closest('.enhanced-card');
      fireEvent.click(cardElement!);
      
      // Should call onReveal after flip animation delay
      await waitFor(() => {
        expect(mockOnReveal).toHaveBeenCalledWith('test-card-1');
      }, { timeout: 500 });
    });

    test('does not call onReveal when revealed card is clicked', () => {
      const mockOnReveal = jest.fn();
      const revealedCard = { ...mockCard, isRevealed: true };
      renderWithTheme(<EnhancedCard {...defaultProps} card={revealedCard} onReveal={mockOnReveal} />);
      
      const cardElement = screen.getByText('Test Card').closest('.enhanced-card');
      fireEvent.click(cardElement!);
      
      expect(mockOnReveal).not.toHaveBeenCalled();
    });

    test('does not call onReveal when card is drawing', () => {
      const mockOnReveal = jest.fn();
      renderWithTheme(<EnhancedCard {...defaultProps} onReveal={mockOnReveal} isDrawing={true} />);
      
      const cardElement = screen.getByAltText('Card back').closest('.enhanced-card');
      fireEvent.click(cardElement!);
      
      expect(mockOnReveal).not.toHaveBeenCalled();
    });

    test('toggles guidance panel when guidance button is clicked', () => {
      const revealedCard = { ...mockCard, isRevealed: true };
      renderWithTheme(<EnhancedCard {...defaultProps} card={revealedCard} />);
      
      const guidanceButton = screen.getByTestId('auto-stories-icon').closest('button');
      fireEvent.click(guidanceButton!);
      
      expect(screen.getByText('Guidance & Reflection')).toBeInTheDocument();
      expect(screen.getByText('What does this test card mean to you?')).toBeInTheDocument();
      expect(screen.getByTestId('expand-less-icon')).toBeInTheDocument();
    });

    test('shows reversed guidance when card is reversed', () => {
      const reversedCard = { ...mockCard, isRevealed: true, isReversed: true };
      renderWithTheme(<EnhancedCard {...defaultProps} card={reversedCard} />);
      
      const guidanceButton = screen.getByTestId('auto-stories-icon').closest('button');
      fireEvent.click(guidanceButton!);
      
      expect(screen.getByText('How does the reversal apply to your situation?')).toBeInTheDocument();
    });

    test('closes guidance panel when clicked again', () => {
      const revealedCard = { ...mockCard, isRevealed: true };
      renderWithTheme(<EnhancedCard {...defaultProps} card={revealedCard} />);
      
      const guidanceButton = screen.getByTestId('auto-stories-icon').closest('button');
      
      // Open guidance
      fireEvent.click(guidanceButton!);
      expect(screen.getByText('Guidance & Reflection')).toBeInTheDocument();
      
      // Close guidance
      fireEvent.click(guidanceButton!);
      expect(screen.queryByText('Guidance & Reflection')).not.toBeInTheDocument();
    });
  });

  describe('Responsive Sizing', () => {
    test('adjusts size for reading mode', () => {
      const revealedCard = { ...mockCard, isRevealed: true };
      renderWithTheme(
        <EnhancedCard 
          {...defaultProps} 
          card={revealedCard} 
          isReading={true}
          containerWidth={800}
        />
      );
      
      // Card should be rendered (specific size testing would require more complex setup)
      expect(screen.getByText('Test Card')).toBeInTheDocument();
    });

    test('adjusts size for different container widths', () => {
      const revealedCard = { ...mockCard, isRevealed: true };
      renderWithTheme(
        <EnhancedCard 
          {...defaultProps} 
          card={revealedCard} 
          isReading={true}
          containerWidth={600}
        />
      );
      
      expect(screen.getByText('Test Card')).toBeInTheDocument();
    });
  });

  describe('Animation States', () => {
    test('shows drawing animation when isDrawing is true', () => {
      renderWithTheme(<EnhancedCard {...defaultProps} isDrawing={true} />);
      
      // Card should be rendered with drawing animation
      expect(screen.getByAltText('Card back')).toBeInTheDocument();
    });

    test('shows hover effects when not drawing and not revealed', () => {
      renderWithTheme(<EnhancedCard {...defaultProps} />);
      
      const cardElement = screen.getByAltText('Card back').closest('.enhanced-card');
      expect(cardElement).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    test('has proper alt text for card back image', () => {
      renderWithTheme(<EnhancedCard {...defaultProps} />);
      
      expect(screen.getByAltText('Card back')).toBeInTheDocument();
    });

    test('has proper button for guidance toggle', () => {
      const revealedCard = { ...mockCard, isRevealed: true };
      renderWithTheme(<EnhancedCard {...defaultProps} card={revealedCard} />);
      
      const guidanceButton = screen.getByTestId('auto-stories-icon').closest('button');
      expect(guidanceButton).toBeInTheDocument();
      expect(guidanceButton).toHaveAttribute('type', 'button');
    });

    test('has proper heading structure', () => {
      const revealedCard = { ...mockCard, isRevealed: true };
      renderWithTheme(<EnhancedCard {...defaultProps} card={revealedCard} />);
      
      // Position label should be h6
      expect(screen.getByRole('heading', { level: 6, name: 'Test Position' })).toBeInTheDocument();
      
      // Card title should be h3
      expect(screen.getByRole('heading', { level: 3, name: 'Test Card' })).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    test('handles empty card content gracefully', () => {
      const emptyCard = {
        ...mockCard,
        title: '',
        upright_meaning: '',
        upright_key_interpretation: '',
        upright_affirmation: '',
        isRevealed: true
      };
      
      renderWithTheme(<EnhancedCard {...defaultProps} card={emptyCard} />);
      
      // Should still render the card structure
      expect(screen.getByText('Test Suite')).toBeInTheDocument();
    });

    test('handles very long card content', () => {
      const longContentCard = {
        ...mockCard,
        upright_meaning: 'This is a very long meaning that goes on and on and should still be displayed properly within the card layout without breaking the design or causing overflow issues.',
        isRevealed: true
      };
      
      renderWithTheme(<EnhancedCard {...defaultProps} card={longContentCard} />);
      
      expect(screen.getByText(/This is a very long meaning/)).toBeInTheDocument();
    });

    test('handles missing position label', () => {
      const positionWithoutLabel = { x: 0, y: 0, label: '' };
      renderWithTheme(<EnhancedCard {...defaultProps} position={positionWithoutLabel} />);
      
      // Should still render the card
      expect(screen.getByAltText('Card back')).toBeInTheDocument();
    });
  });
});
