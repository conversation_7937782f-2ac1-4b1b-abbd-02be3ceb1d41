export interface OracleCard {
  title: string;
  suite: string;
  upright_meaning: string;
  upright_key_interpretation: string;
  upright_affirmation: string;
  upright_journal_prompt: string;
  reversal_key_interpretation: string;
  reversal_journal_prompt: string;
  reversal_meaning: string;
  reversal_affirmation: string;
}

export interface DrawnCard extends OracleCard {
  id: string;
  isRevealed: boolean;
  isReversed: boolean;
  position: number;
  isSelected?: boolean;
  isHovered?: boolean;
  spreadPosition?: { x: number; y: number; rotation: number };
}

export type SpreadType = 'single' | 'three';

export type DeckState = 'stacked' | 'spreading' | 'spread' | 'selecting' | 'drawing';

export interface CardAnimation {
  x: number;
  y: number;
  rotation: number;
  scale: number;
  zIndex: number;
  opacity: number;
}

export interface ReadingState {
  phase: 'question' | 'sacred-space' | 'deck-interaction' | 'card-selection' | 'card-drawing' | 'card-revealing' | 'reading' | 'complete';
  spreadType: SpreadType | null;
  drawnCards: DrawnCard[];
  deckState: DeckState;
  isShuffling: boolean;
  deckShuffled: boolean;
  selectedCardIds: string[];
  isDrawingCards: boolean;
  showGuidance: boolean;
}
