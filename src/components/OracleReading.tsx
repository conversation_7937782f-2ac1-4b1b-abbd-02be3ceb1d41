import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Typography, Box, Stack, Container } from '@mui/material';
import { OracleCard, ReadingState, SpreadType, DrawnCard } from '../types/Card';
import { drawCards, getCardCount, getReadingPositions, sleep } from '../utils/cardUtils';
import { ImmersiveDeck } from './ImmersiveDeck';
import { EnhancedCard } from './EnhancedCard';
import { MysticalButton } from './MysticalButton';
import { colors } from '../theme/muiTheme';
import oracleCardsData from '../assets/falling_bird_oracle_cards.json';

export const OracleReading: React.FC = () => {
  const [cards] = useState<OracleCard[]>(oracleCardsData);
  const [readingState, setReadingState] = useState<ReadingState>({
    phase: 'question',
    spreadType: null,
    drawnCards: [],
    deckState: 'stacked',
    isShuffling: false,
    deckShuffled: false,
    selectedCardIds: [],
    isDrawingCards: false,
    showGuidance: false
  });
  const [selectedCardIndices, setSelectedCardIndices] = useState<number[]>([]);

  const handleShuffle = async () => {
    setReadingState(prev => ({ ...prev, isShuffling: true, deckState: 'stacked' }));
    await sleep(2000); // Shuffle animation duration
    setReadingState(prev => ({
      ...prev,
      isShuffling: false,
      deckShuffled: true,
      phase: 'deck-interaction'
    }));
  };

  const handleSpreadSelection = (spreadType: SpreadType) => {
    setReadingState(prev => ({
      ...prev,
      spreadType,
      phase: 'deck-interaction',
      deckState: 'spreading'
    }));
  };

  const handleDeckSpreadComplete = useCallback(() => {
    setReadingState(prev => ({
      ...prev,
      deckState: 'spread',
      phase: 'card-selection'
    }));
  }, []);

  const handleCardSelect = useCallback((cardIndex: number) => {
    setSelectedCardIndices(prev => {
      const newSelection = [...prev, cardIndex];
      const maxCards = getCardCount(readingState.spreadType!);

      if (newSelection.length === maxCards) {
        // All cards selected, start drawing phase
        setTimeout(() => {
          const selectedCards = newSelection.map(index => cards[index]);
          const drawnCards = selectedCards.map((card, position) => ({
            ...card,
            id: `card-${Date.now()}-${position}`,
            isRevealed: false,
            isReversed: Math.random() < 0.3,
            position,
            isSelected: true
          }));

          setReadingState(prev => ({
            ...prev,
            drawnCards,
            phase: 'card-drawing',
            deckState: 'drawing',
            isDrawingCards: true
          }));

          // Transition to revealing phase
          setTimeout(() => {
            setReadingState(prev => ({
              ...prev,
              phase: 'card-revealing',
              isDrawingCards: false
            }));
          }, 2000);
        }, 500);
      }

      return newSelection;
    });
  }, [cards, readingState.spreadType]);

  const handleCardReveal = useCallback((cardId: string) => {
    setReadingState(prev => {
      const updatedCards = prev.drawnCards.map(card =>
        card.id === cardId ? { ...card, isRevealed: true } : card
      );

      // Check if all cards are revealed
      const allRevealed = updatedCards.every(card => card.isRevealed);

      return {
        ...prev,
        drawnCards: updatedCards,
        phase: allRevealed ? 'reading' : prev.phase
      };
    });
  }, []);

  const handleCompleteReading = useCallback(() => {
    setReadingState(prev => ({ ...prev, phase: 'complete' }));
  }, []);

  const resetReading = useCallback(() => {
    setReadingState({
      phase: 'question',
      spreadType: null,
      drawnCards: [],
      deckState: 'stacked',
      isShuffling: false,
      deckShuffled: false,
      selectedCardIds: [],
      isDrawingCards: false,
      showGuidance: false
    });
    setSelectedCardIndices([]);
  }, []);

  const renderPhase = () => {
    switch (readingState.phase) {
      case 'question':
        return (
          <Container
            maxWidth="lg"
            component={motion.div}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              position: 'relative',
              py: 4,
            }}
          >
            <Box
              sx={{
                maxWidth: 700,
                textAlign: 'center',
                background: `linear-gradient(135deg, rgba(74, 44, 90, 0.4) 0%, rgba(26, 26, 46, 0.6) 100%)`,
                border: `2px solid ${colors.featherGold}`,
                borderRadius: '20px',
                boxShadow: `0 8px 32px ${colors.mysticalGlow}, inset 0 1px 0 rgba(245, 243, 240, 0.1)`,
                backdropFilter: 'blur(15px)',
                p: { xs: 3, md: 5 },
                position: 'relative',
                '&::before': {
                  content: '"🌙"',
                  position: 'absolute',
                  top: '-15px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  fontSize: '2rem',
                  background: colors.cosmicVoid,
                  padding: '0 1rem',
                },
              }}
            >
              <Typography
                variant="h2"
                component="h2"
                sx={{
                  mb: 3,
                  color: colors.featherGold,
                  fontFamily: '"Cinzel", serif',
                  fontSize: { xs: '1.8rem', md: '2.5rem' }
                }}
              >
                Sacred Inquiry
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  mb: 4,
                  color: colors.etherealWhite,
                  fontStyle: 'italic',
                  fontSize: { xs: '1rem', md: '1.1rem' },
                  fontFamily: '"Lora", serif',
                  lineHeight: 1.6
                }}
              >
                Enter the sacred space of inquiry. Take a deep breath and center yourself.
                What wisdom does your soul seek from the cosmic oracle?
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  mb: 4,
                  color: colors.moonlightSilver,
                  fontStyle: 'italic',
                  fontFamily: '"Lora", serif',
                  fontSize: { xs: '0.9rem', md: '1rem' }
                }}
              >
                Hold your question gently in your heart...
              </Typography>

              <Box sx={{ mt: 4 }}>
                <MysticalButton
                  onClick={() => setReadingState(prev => ({ ...prev, phase: 'sacred-space' }))}
                  size="large"
                  sx={{ px: 4, py: 1.5 }}
                >
                  Enter Sacred Space
                </MysticalButton>
              </Box>
            </Box>
          </Container>
        );

      case 'sacred-space':
        return (
          <Container
            maxWidth="lg"
            component={motion.div}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              position: 'relative',
              py: 4,
            }}
          >
            <Box sx={{ textAlign: 'center', mb: 6 }}>
              <Typography
                variant="h2"
                component="h2"
                sx={{
                  mb: 4,
                  color: colors.featherGold,
                  fontFamily: '"Cinzel", serif',
                  fontSize: { xs: '1.8rem', md: '2.5rem' }
                }}
              >
                Choose Your Path
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  mb: 6,
                  color: colors.etherealWhite,
                  fontStyle: 'italic',
                  fontFamily: '"Lora", serif',
                  fontSize: { xs: '1rem', md: '1.1rem' }
                }}
              >
                Select the spread that calls to your spirit
              </Typography>
            </Box>

            <Stack
              direction={{ xs: 'column', md: 'row' }}
              spacing={4}
              justifyContent="center"
              alignItems="center"
              sx={{ width: '100%', maxWidth: 900 }}
            >
              <Box
                component={motion.div}
                className="spread-option"
                onClick={() => handleSpreadSelection('single')}
                whileHover={{ scale: 1.02, y: -5 }}
                whileTap={{ scale: 0.98 }}
                sx={{
                  background: `linear-gradient(135deg, rgba(74, 44, 90, 0.4) 0%, rgba(26, 26, 46, 0.6) 100%)`,
                  border: `2px solid ${colors.featherGold}`,
                  borderRadius: '20px',
                  boxShadow: `0 8px 32px ${colors.mysticalGlow}, inset 0 1px 0 rgba(245, 243, 240, 0.1)`,
                  backdropFilter: 'blur(15px)',
                  p: 4,
                  minWidth: { xs: 280, md: 350 },
                  minHeight: 220,
                  cursor: 'pointer',
                  textAlign: 'center',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  transition: 'all 0.4s ease',
                  position: 'relative',
                  '&::before': {
                    content: '"✦"',
                    position: 'absolute',
                    top: '-15px',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    background: colors.cosmicVoid,
                    color: colors.featherGold,
                    padding: '0 1rem',
                    fontSize: '1.5rem',
                  },
                  '&:hover': {
                    boxShadow: `0 15px 40px ${colors.mysticalGlow}, 0 0 30px ${colors.bioluminescentBlue}`,
                  }
                }}
              >
                <Typography
                  variant="h4"
                  component="h3"
                  sx={{
                    mb: 2,
                    color: colors.featherGold,
                    fontFamily: '"Cinzel", serif'
                  }}
                >
                  Single Card
                </Typography>
                <Typography
                  variant="body1"
                  sx={{
                    color: colors.etherealWhite,
                    fontStyle: 'italic',
                    fontFamily: '"Lora", serif'
                  }}
                >
                  One card for focused guidance and clarity
                </Typography>
              </Box>

              <Box
                component={motion.div}
                className="spread-option"
                onClick={() => handleSpreadSelection('three')}
                whileHover={{ scale: 1.02, y: -5 }}
                whileTap={{ scale: 0.98 }}
                sx={{
                  background: `linear-gradient(135deg, rgba(74, 44, 90, 0.4) 0%, rgba(26, 26, 46, 0.6) 100%)`,
                  border: `2px solid ${colors.featherGold}`,
                  borderRadius: '20px',
                  boxShadow: `0 8px 32px ${colors.mysticalGlow}, inset 0 1px 0 rgba(245, 243, 240, 0.1)`,
                  backdropFilter: 'blur(15px)',
                  p: 4,
                  minWidth: { xs: 280, md: 350 },
                  minHeight: 220,
                  cursor: 'pointer',
                  textAlign: 'center',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  transition: 'all 0.4s ease',
                  position: 'relative',
                  '&::before': {
                    content: '"✦ ◊ ✦"',
                    position: 'absolute',
                    top: '-15px',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    background: colors.cosmicVoid,
                    color: colors.featherGold,
                    padding: '0 1rem',
                    fontSize: '1rem',
                    letterSpacing: '4px',
                  },
                  '&:hover': {
                    boxShadow: `0 15px 40px ${colors.mysticalGlow}, 0 0 30px ${colors.bioluminescentBlue}`,
                  }
                }}
              >
                <Typography
                  variant="h4"
                  component="h3"
                  sx={{
                    mb: 2,
                    color: colors.featherGold,
                    fontFamily: '"Cinzel", serif'
                  }}
                >
                  Three Card Spread
                </Typography>
                <Typography
                  variant="body1"
                  sx={{
                    color: colors.etherealWhite,
                    fontStyle: 'italic',
                    fontFamily: '"Lora", serif'
                  }}
                >
                  Past, Present, Future wisdom
                </Typography>
              </Box>
            </Stack>
          </Container>
        );

      case 'deck-interaction':
        return (
          <Container
            maxWidth="xl"
            component={motion.div}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              position: 'relative',
              py: 2,
            }}
          >
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <Typography
                variant="h2"
                component="h2"
                sx={{
                  mb: 2,
                  color: colors.featherGold,
                  fontFamily: '"Cinzel", serif',
                  fontSize: { xs: '1.6rem', md: '2.2rem' }
                }}
              >
                {readingState.deckState === 'spreading' ? 'Cards Awakening...' : 'Shuffle the Cosmic Deck'}
              </Typography>
              {readingState.deckState === 'stacked' && (
                <Typography
                  variant="body1"
                  sx={{
                    mb: 4,
                    color: colors.etherealWhite,
                    fontStyle: 'italic',
                    fontFamily: '"Lora", serif'
                  }}
                >
                  Infuse the cards with your energy and intention
                </Typography>
              )}
            </Box>

            <Box sx={{ flex: 1, width: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <ImmersiveDeck
                cards={cards}
                deckState={readingState.deckState}
                onCardSelect={handleCardSelect}
                onSpreadComplete={handleDeckSpreadComplete}
                selectedCardCount={selectedCardIndices.length}
                maxSelections={getCardCount(readingState.spreadType!)}
                isShuffling={readingState.isShuffling}
              />
            </Box>

            {readingState.deckState === 'stacked' && (
              <Box sx={{ textAlign: 'center' }}>
                <MysticalButton
                  onClick={handleShuffle}
                  disabled={readingState.isShuffling}
                  size="large"
                  sx={{ px: 4, py: 1.5 }}
                >
                  {readingState.isShuffling ? 'Shuffling...' : 'Shuffle & Spread Cards'}
                </MysticalButton>
              </Box>
            )}
          </Container>
        );

      case 'card-selection':
        return (
          <Container
            maxWidth="xl"
            component={motion.div}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              position: 'relative',
              py: 2,
            }}
          >
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <Typography
                variant="h2"
                component="h2"
                sx={{
                  mb: 2,
                  color: colors.featherGold,
                  fontFamily: '"Cinzel", serif',
                  fontSize: { xs: '1.6rem', md: '2.2rem' }
                }}
              >
                Choose Your Cards
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  mb: 4,
                  color: colors.etherealWhite,
                  fontStyle: 'italic',
                  fontFamily: '"Lora", serif'
                }}
              >
                Let your intuition guide you to the cards that call to your spirit
              </Typography>
            </Box>

            <Box sx={{ flex: 1, width: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <ImmersiveDeck
                cards={cards}
                deckState={readingState.deckState}
                onCardSelect={handleCardSelect}
                onSpreadComplete={handleDeckSpreadComplete}
                selectedCardCount={selectedCardIndices.length}
                maxSelections={getCardCount(readingState.spreadType!)}
                isShuffling={false}
              />
            </Box>
          </Container>
        );

      case 'card-drawing':
        return (
          <Container
            maxWidth="lg"
            component={motion.div}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Box sx={{ textAlign: 'center', mb: 6 }}>
              <Typography
                variant="h2"
                component="h2"
                sx={{
                  mb: 4,
                  color: colors.featherGold,
                  fontFamily: '"Cinzel", serif',
                  fontSize: { xs: '1.8rem', md: '2.5rem' }
                }}
              >
                Drawing Your Sacred Cards...
              </Typography>
              <Box sx={{ fontSize: { xs: '3rem', md: '5rem' }, mb: 4 }}>
                <motion.div
                  initial={{ scale: 0.8, rotate: 0 }}
                  animate={{
                    scale: [0.8, 1.2, 1],
                    rotate: [0, 180, 360],
                  }}
                  transition={{
                    duration: 2,
                    ease: "easeInOut",
                    repeat: Infinity,
                    repeatType: "loop"
                  }}
                >
                  ✨
                </motion.div>
              </Box>
              <Typography
                variant="body1"
                sx={{
                  color: colors.etherealWhite,
                  fontStyle: 'italic',
                  fontFamily: '"Lora", serif',
                  fontSize: { xs: '1rem', md: '1.1rem' }
                }}
              >
                The universe aligns your chosen cards with cosmic wisdom...
              </Typography>
            </Box>
          </Container>
        );

      case 'card-revealing':
        const revealingPositions = getReadingPositions(readingState.spreadType!, 1200);
        return (
          <Container
            maxWidth="xl"
            component={motion.div}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              py: 2,
            }}
          >
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <Typography
                variant="h2"
                component="h2"
                sx={{
                  mb: 2,
                  color: colors.featherGold,
                  fontFamily: '"Cinzel", serif',
                  fontSize: { xs: '1.6rem', md: '2.2rem' }
                }}
              >
                Your Sacred Cards Await
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  color: colors.etherealWhite,
                  fontStyle: 'italic',
                  fontFamily: '"Lora", serif'
                }}
              >
                Click each card to reveal its wisdom
              </Typography>
            </Box>

            <Box
              sx={{
                display: 'flex',
                flexDirection: readingState.spreadType === 'single' ? 'column' : { xs: 'column', lg: 'row' },
                gap: { xs: 2, lg: 3 },
                alignItems: 'center',
                justifyContent: 'center',
                width: '100%',
                maxWidth: '100%',
                overflow: 'hidden',
              }}
            >
              {readingState.drawnCards.map((card, index) => (
                <Box
                  key={card.id}
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    flex: readingState.spreadType === 'three' ? 1 : 'none',
                    maxWidth: '100%'
                  }}
                >
                  <EnhancedCard
                    card={card}
                    onReveal={handleCardReveal}
                    position={revealingPositions[index]}
                    isDrawing={false}
                    isReading={false}
                    containerWidth={1200}
                  />
                </Box>
              ))}
            </Box>
          </Container>
        );

      case 'reading':
        const readingPositions = getReadingPositions(readingState.spreadType!, 1200);
        const allRevealed = readingState.drawnCards.every(card => card.isRevealed);

        return (
          <Container
            maxWidth="xl"
            component={motion.div}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              py: 2,
              overflow: 'hidden',
            }}
          >
            <Box sx={{ textAlign: 'center', mb: 3 }}>
              <Typography
                variant="h2"
                component="h2"
                sx={{
                  mb: 2,
                  color: colors.featherGold,
                  fontFamily: '"Cinzel", serif',
                  fontSize: { xs: '1.6rem', md: '2.2rem' }
                }}
              >
                Your Sacred Reading
              </Typography>
              {!allRevealed && (
                <Typography
                  variant="body1"
                  sx={{
                    color: colors.etherealWhite,
                    fontStyle: 'italic',
                    fontFamily: '"Lora", serif'
                  }}
                >
                  Explore the wisdom within each card
                </Typography>
              )}
            </Box>

            <Box
              sx={{
                flex: 1,
                display: 'flex',
                flexDirection: readingState.spreadType === 'single' ? 'column' : { xs: 'column', lg: 'row' },
                gap: { xs: 2, lg: 2 },
                alignItems: 'flex-start',
                justifyContent: 'center',
                width: '100%',
                overflow: 'auto',
                px: { xs: 1, md: 2 },
              }}
            >
              {readingState.drawnCards.map((card, index) => (
                <Box
                  key={card.id}
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    flex: readingState.spreadType === 'three' ? 1 : 'none',
                    width: readingState.spreadType === 'single' ? '100%' : 'auto',
                    maxWidth: '100%'
                  }}
                >
                  <EnhancedCard
                    card={card}
                    onReveal={handleCardReveal}
                    position={readingPositions[index]}
                    isDrawing={false}
                    isReading={true}
                    containerWidth={1200}
                  />
                </Box>
              ))}
            </Box>

            {allRevealed && (
              <Box
                component={motion.div}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1 }}
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  mt: 3,
                  pb: 2,
                }}
              >
                <Box
                  sx={{
                    background: `linear-gradient(135deg, rgba(74, 44, 90, 0.4) 0%, rgba(26, 26, 46, 0.6) 100%)`,
                    border: `2px solid ${colors.featherGold}`,
                    borderRadius: '15px',
                    boxShadow: `0 8px 32px ${colors.mysticalGlow}, inset 0 1px 0 rgba(245, 243, 240, 0.1)`,
                    backdropFilter: 'blur(15px)',
                    p: 3,
                    textAlign: 'center',
                    position: 'relative',
                    '&::before': {
                      content: '"✦ ◊ ✦"',
                      position: 'absolute',
                      top: '-15px',
                      left: '50%',
                      transform: 'translateX(-50%)',
                      background: colors.cosmicVoid,
                      color: colors.featherGold,
                      padding: '0 1rem',
                      fontSize: '1rem',
                      letterSpacing: '4px',
                    },
                  }}
                >
                  <Typography
                    variant="body1"
                    sx={{
                      mb: 3,
                      color: colors.etherealWhite,
                      fontStyle: 'italic',
                      fontSize: '1.1rem',
                      fontFamily: '"Lora", serif'
                    }}
                  >
                    Your reading is complete. Take time to reflect on the sacred messages.
                  </Typography>
                  <MysticalButton
                    onClick={handleCompleteReading}
                    size="large"
                  >
                    Complete Reading
                  </MysticalButton>
                </Box>
              </Box>
            )}
          </Container>
        );

      case 'complete':
        return (
          <Container
            maxWidth="lg"
            component={motion.div}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              py: 4,
            }}
          >
            <Box
              sx={{
                textAlign: 'center',
                background: `linear-gradient(135deg, rgba(74, 44, 90, 0.4) 0%, rgba(26, 26, 46, 0.6) 100%)`,
                border: `2px solid ${colors.featherGold}`,
                borderRadius: '20px',
                boxShadow: `0 8px 32px ${colors.mysticalGlow}, inset 0 1px 0 rgba(245, 243, 240, 0.1)`,
                backdropFilter: 'blur(15px)',
                p: { xs: 4, md: 6 },
                maxWidth: 600,
                position: 'relative',
                '&::before': {
                  content: '"🌟"',
                  position: 'absolute',
                  top: '-15px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  fontSize: '2rem',
                  background: colors.cosmicVoid,
                  padding: '0 1rem',
                },
              }}
            >
              <Typography
                variant="h2"
                component="h2"
                sx={{
                  mb: 4,
                  color: colors.featherGold,
                  fontFamily: '"Cinzel", serif',
                  fontSize: { xs: '1.8rem', md: '2.5rem' }
                }}
              >
                Journey Complete
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  mb: 4,
                  color: colors.etherealWhite,
                  fontStyle: 'italic',
                  fontSize: { xs: '1rem', md: '1.1rem' },
                  fontFamily: '"Lora", serif',
                  lineHeight: 1.6
                }}
              >
                The cosmic oracle has shared its wisdom. Carry these insights with you as you walk your path.
                May the guidance illuminate your way forward.
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  mb: 6,
                  color: colors.moonlightSilver,
                  fontStyle: 'italic',
                  fontFamily: '"Lora", serif'
                }}
              >
                Trust in the wisdom you have received.
              </Typography>

              <MysticalButton
                onClick={resetReading}
                size="large"
                sx={{ px: 4, py: 1.5 }}
              >
                Begin New Journey
              </MysticalButton>
            </Box>
          </Container>
        );

      default:
        return null;
    }
  };

  return (
    <Box
      className="oracle-reading"
      sx={{
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      <AnimatePresence mode="wait">
        {renderPhase()}
      </AnimatePresence>
    </Box>
  );
};
