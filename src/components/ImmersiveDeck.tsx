import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Box, Typography } from '@mui/material';
import { OracleCard, DeckState, CardAnimation } from '../types/Card';
import { generateDeckSpreadPositions, generateStackedDeckPositions, sleep } from '../utils/cardUtils';
import { colors } from '../theme/muiTheme';
import cardBackImage from '../assets/card-back.png';

interface ImmersiveDeckProps {
  cards: OracleCard[];
  deckState: DeckState;
  onCardSelect: (cardIndex: number) => void;
  onSpreadComplete: () => void;
  selectedCardCount: number;
  maxSelections: number;
  isShuffling: boolean;
}

export const ImmersiveDeck: React.FC<ImmersiveDeckProps> = ({
  cards,
  deckState,
  onCardSelect,
  onSpreadComplete,
  selectedCardCount,
  maxSelections,
  isShuffling
}) => {
  const [cardPositions, setCardPositions] = useState<CardAnimation[]>([]);
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);
  const [selectedCards, setSelectedCards] = useState<Set<number>>(new Set());

  // Initialize card positions based on deck state
  useEffect(() => {
    const initializePositions = async () => {
      switch (deckState) {
        case 'stacked':
          setCardPositions(generateStackedDeckPositions(cards.length));
          break;
        case 'spreading':
          // Animate from stacked to spread
          await sleep(300);
          setCardPositions(generateDeckSpreadPositions(Math.min(cards.length, 15)));
          setTimeout(() => onSpreadComplete(), 1500);
          break;
        case 'spread':
          setCardPositions(generateDeckSpreadPositions(Math.min(cards.length, 15)));
          break;
        default:
          setCardPositions(generateStackedDeckPositions(cards.length));
      }
    };

    initializePositions();
  }, [deckState, cards.length, onSpreadComplete]);

  const handleCardClick = useCallback((cardIndex: number) => {
    if (deckState !== 'spread' && deckState !== 'selecting') return;
    if (selectedCards.has(cardIndex)) return;
    if (selectedCardCount >= maxSelections) return;

    setSelectedCards(prev => new Set([...prev, cardIndex]));
    onCardSelect(cardIndex);
  }, [deckState, selectedCards, selectedCardCount, maxSelections, onCardSelect]);

  const handleCardHover = useCallback((cardIndex: number | null) => {
    if (deckState === 'spread' || deckState === 'selecting') {
      setHoveredCard(cardIndex);
    }
  }, [deckState]);

  const getCardStyle = (index: number): React.CSSProperties => {
    const position = cardPositions[index];
    if (!position) return {};

    const isHovered = hoveredCard === index && !selectedCards.has(index);
    const isSelected = selectedCards.has(index);

    return {
      transform: `
        translate(${position.x}px, ${position.y + (isHovered ? -10 : 0) + (isSelected ? -30 : 0)}px) 
        rotate(${position.rotation}deg) 
        scale(${position.scale * (isHovered ? 1.05 : 1) * (isSelected ? 1.1 : 1)})
      `,
      zIndex: position.zIndex + (isHovered ? 100 : 0) + (isSelected ? 200 : 0),
      opacity: position.opacity * (isSelected ? 0.7 : 1),
    };
  };

  const renderCard = (cardIndex: number) => {
    const isSelectable = (deckState === 'spread' || deckState === 'selecting') && 
                        !selectedCards.has(cardIndex) && 
                        selectedCardCount < maxSelections;
    const isSelected = selectedCards.has(cardIndex);

    return (
      <Box
        key={cardIndex}
        component={motion.div}
        className="deck-card"
        onClick={() => handleCardClick(cardIndex)}
        onMouseEnter={() => handleCardHover(cardIndex)}
        onMouseLeave={() => handleCardHover(null)}
        animate={isShuffling ? {
          x: [0, Math.random() * 20 - 10, 0],
          y: [0, Math.random() * 20 - 10, 0],
          rotate: [0, Math.random() * 10 - 5, 0],
        } : {}}
        transition={{
          duration: 0.5,
          delay: cardIndex * 0.05,
          repeat: isShuffling ? Infinity : 0,
          repeatType: "reverse"
        }}
        sx={{
          position: 'absolute',
          width: { xs: 80, sm: 100, md: 120 },
          height: { xs: 120, sm: 150, md: 180 },
          borderRadius: '12px',
          overflow: 'hidden',
          cursor: isSelectable ? 'pointer' : 'default',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          transformOrigin: 'center center',
          ...getCardStyle(cardIndex),
          boxShadow: `
            0 4px 20px rgba(10, 10, 15, 0.4),
            0 0 0 ${isSelected ? '3px' : '2px'} ${colors.featherGold},
            ${hoveredCard === cardIndex ? `0 0 20px ${colors.mysticalGlow}` : ''}
          `,
          border: `1px solid rgba(212, 175, 55, ${isSelected ? '0.8' : '0.3'})`,
          '&:hover': isSelectable ? {
            filter: `brightness(1.1) drop-shadow(0 0 15px ${colors.bioluminescentBlue})`,
          } : {},
        }}
      >
        <Box
          component="img"
          src={cardBackImage}
          alt="Oracle card back"
          sx={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            filter: 'sepia(20%) saturate(1.2) brightness(0.9)',
            transition: 'filter 0.3s ease',
          }}
        />
        
        {/* Selection indicator */}
        {isSelected && (
          <Box
            component={motion.div}
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              width: 40,
              height: 40,
              borderRadius: '50%',
              background: `linear-gradient(135deg, ${colors.featherGold} 0%, ${colors.bioluminescentBlue} 100%)`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '1.5rem',
              color: colors.cosmicVoid,
              fontWeight: 'bold',
              boxShadow: `0 0 20px ${colors.mysticalGlow}`,
            }}
          >
            ✓
          </Box>
        )}

        {/* Hover glow effect */}
        {hoveredCard === cardIndex && !isSelected && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: `radial-gradient(circle, ${colors.bioluminescentBlue}20 0%, transparent 70%)`,
              pointerEvents: 'none',
            }}
          />
        )}
      </Box>
    );
  };

  return (
    <Box
      className="immersive-deck"
      data-testid="immersive-deck"
      sx={{
        position: 'relative',
        width: '100%',
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: { xs: 300, md: 400 },
      }}
    >
      {/* Deck container */}
      <Box
        sx={{
          position: 'relative',
          width: { xs: 300, sm: 500, md: 700 },
          height: { xs: 200, sm: 300, md: 400 },
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        {cardPositions.map((_, index) => 
          index < Math.min(cards.length, deckState === 'stacked' ? 8 : 15) && renderCard(index)
        )}
      </Box>

      {/* Selection counter */}
      {(deckState === 'spread' || deckState === 'selecting') && (
        <Box
          component={motion.div}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          sx={{
            position: 'absolute',
            bottom: { xs: 20, md: 40 },
            left: '50%',
            transform: 'translateX(-50%)',
            background: `linear-gradient(135deg, rgba(74, 44, 90, 0.9) 0%, rgba(26, 26, 46, 0.9) 100%)`,
            border: `2px solid ${colors.featherGold}`,
            borderRadius: '15px',
            px: 3,
            py: 1.5,
            backdropFilter: 'blur(10px)',
          }}
        >
          <Typography
            variant="body1"
            sx={{
              color: colors.etherealWhite,
              fontFamily: '"Cinzel", serif',
              fontSize: { xs: '0.9rem', md: '1rem' },
              textAlign: 'center',
            }}
          >
            {selectedCardCount} of {maxSelections} cards selected
          </Typography>
        </Box>
      )}
    </Box>
  );
};
