
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/components</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/components</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">73.75% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>163/221</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">76.49% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>166/217</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">47.54% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>29/61</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">76.96% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>147/191</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="Card.tsx"><a href="Card.tsx.html">Card.tsx</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="14" class="abs high">14/14</td>
	<td data-value="98.11" class="pct high">98.11%</td>
	<td data-value="53" class="abs high">52/53</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="13" class="abs high">13/13</td>
	</tr>

<tr>
	<td class="file high" data-value="Deck.tsx"><a href="Deck.tsx.html">Deck.tsx</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="14" class="abs high">14/14</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="12" class="abs high">12/12</td>
	</tr>

<tr>
	<td class="file high" data-value="EnhancedCard.tsx"><a href="EnhancedCard.tsx.html">EnhancedCard.tsx</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="25" class="abs high">25/25</td>
	<td data-value="93.61" class="pct high">93.61%</td>
	<td data-value="47" class="abs high">44/47</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="5" class="abs high">5/5</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="24" class="abs high">24/24</td>
	</tr>

<tr>
	<td class="file high" data-value="Guidebook.tsx"><a href="Guidebook.tsx.html">Guidebook.tsx</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="9" class="abs high">9/9</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="11" class="abs high">11/11</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="8" class="abs high">8/8</td>
	</tr>

<tr>
	<td class="file high" data-value="ImmersiveDeck.tsx"><a href="ImmersiveDeck.tsx.html">ImmersiveDeck.tsx</a></td>
	<td data-value="88.88" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 88%"></div><div class="cover-empty" style="width: 12%"></div></div>
	</td>
	<td data-value="88.88" class="pct high">88.88%</td>
	<td data-value="54" class="abs high">48/54</td>
	<td data-value="79.66" class="pct medium">79.66%</td>
	<td data-value="59" class="abs medium">47/59</td>
	<td data-value="76.92" class="pct medium">76.92%</td>
	<td data-value="13" class="abs medium">10/13</td>
	<td data-value="91.48" class="pct high">91.48%</td>
	<td data-value="47" class="abs high">43/47</td>
	</tr>

<tr>
	<td class="file high" data-value="MysticalButton.tsx"><a href="MysticalButton.tsx.html">MysticalButton.tsx</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="10" class="abs high">10/10</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="9" class="abs high">9/9</td>
	</tr>

<tr>
	<td class="file high" data-value="MysticalContainer.tsx"><a href="MysticalContainer.tsx.html">MysticalContainer.tsx</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="18" class="abs high">18/18</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="5" class="abs high">5/5</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="14" class="abs high">14/14</td>
	</tr>

<tr>
	<td class="file low" data-value="OracleReading.tsx"><a href="OracleReading.tsx.html">OracleReading.tsx</a></td>
	<td data-value="32.46" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 32%"></div><div class="cover-empty" style="width: 68%"></div></div>
	</td>
	<td data-value="32.46" class="pct low">32.46%</td>
	<td data-value="77" class="abs low">25/77</td>
	<td data-value="2.77" class="pct low">2.77%</td>
	<td data-value="36" class="abs low">1/36</td>
	<td data-value="6.45" class="pct low">6.45%</td>
	<td data-value="31" class="abs low">2/31</td>
	<td data-value="37.5" class="pct low">37.5%</td>
	<td data-value="64" class="abs low">24/64</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-08T04:18:50.296Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    